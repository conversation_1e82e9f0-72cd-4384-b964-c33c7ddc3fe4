#!/usr/bin/env python3
"""
生成AI算法进化自组织理论研究流程图和甘特图的图片
使用mermaid-cli工具生成PNG和SVG格式图片
"""

import os
import subprocess
import sys
from datetime import datetime

def create_mermaid_files():
    """创建Mermaid图表文件"""
    
    # 流程图代码
    flowchart_code = """graph TD
    A[🎯 研究启动<br/>2025年8月] --> B[📚 第一阶段：理论基础构建<br/>1-3个月]
    
    B --> B1[📖 文献调研<br/>第1-2个月]
    B --> B2[🧮 数学建模<br/>第2-3个月]
    
    B1 --> B1a[生物进化理论<br/>复杂系统理论<br/>机器学习理论]
    B1 --> B1b[AutoML前沿研究<br/>神经架构搜索<br/>元学习技术]
    B1 --> B1c[理论框架设计<br/>概念定义<br/>评价指标]
    
    B2 --> B2a[进化动力学建模<br/>算法种群动态<br/>适应度景观]
    B2 --> B2b[自组织机制建模<br/>涌现行为表征<br/>复杂性度量]
    B2 --> B2c[优化理论扩展<br/>多目标优化<br/>收敛性分析]
    
    B --> C[🔬 第二阶段：实验设计与验证<br/>4-8个月]
    
    C --> C1[🛠️ 实验平台扩展<br/>第4-5个月]
    C --> C2[🧪 核心实验设计<br/>第5-7个月]
    C --> C3[📊 数据分析验证<br/>第7-8个月]
    
    C1 --> C1a[AutoML-Zero增强<br/>算法基元扩展<br/>分布式计算]
    C1 --> C1b[监控分析工具<br/>可视化系统<br/>性能评估]
    C1 --> C1c[实验环境标准化<br/>基准数据集<br/>评价体系]
    
    C2 --> C2a[实验一：自组织涌现验证<br/>1000代×50次实验]
    C2 --> C2b[实验二：进化动力学研究<br/>5000代连续监控]
    C2 --> C2c[实验三：机制对比实验<br/>10种策略×100次]
    C2 --> C2d[实验四：实际应用验证<br/>5领域×20数据集]
    
    C3 --> C3a[统计分析<br/>进化轨迹特征<br/>性能分布研究]
    C3 --> C3b[理论验证<br/>模型拟合<br/>预测准确性]
    C3 --> C3c[机制解释<br/>现象分析<br/>关键因素识别]
    
    C --> D[📝 第三阶段：成果整理与转化<br/>9-12个月]
    
    D --> D1[📄 学术成果产出<br/>第9-11个月]
    D --> D2[💼 技术转化产业化<br/>第10-12个月]
    D --> D3[🎓 教育推广<br/>第11-12个月]
    
    D1 --> D1a[核心论文撰写<br/>理论论文<br/>实验论文<br/>应用论文]
    D1 --> D1b[会议报告准备<br/>顶级会议投稿<br/>学术演讲]
    D1 --> D1c[专利申请<br/>算法方法专利<br/>系统专利]
    
    D2 --> D2a[产品原型开发<br/>商业化平台<br/>开发工具包]
    D2 --> D2b[商业合作洽谈<br/>技术授权<br/>投资融资]
    D2 --> D2c[开源社区建设<br/>GitHub项目<br/>社区运营]
    
    D3 --> D3a[教学材料开发<br/>在线课程<br/>实验手册]
    D3 --> D3b[学术交流<br/>国际会议<br/>合作研究]
    
    D --> E[🎯 最终成果<br/>2026年7月]
    
    E --> E1[📚 学术成果<br/>3-5篇期刊论文<br/>5-8篇会议论文<br/>完整理论体系]
    E --> E2[💼 技术成果<br/>新一代AutoML系统<br/>5-10项专利<br/>$100K+商业价值]
    E --> E3[🌍 社会影响<br/>技术民主化<br/>产业推动<br/>教育贡献]
    
    %% 样式定义
    classDef phaseBox fill:#e1f5fe,stroke:#01579b,stroke-width:3px
    classDef taskBox fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef subtaskBox fill:#e8f5e8,stroke:#1b5e20,stroke-width:1px
    classDef outcomeBox fill:#fff3e0,stroke:#e65100,stroke-width:3px
    
    class A,B,C,D,E phaseBox
    class B1,B2,C1,C2,C3,D1,D2,D3 taskBox
    class B1a,B1b,B1c,B2a,B2b,B2c,C1a,C1b,C1c,C2a,C2b,C2c,C2d,C3a,C3b,C3c,D1a,D1b,D1c,D2a,D2b,D2c,D3a,D3b subtaskBox
    class E1,E2,E3 outcomeBox"""
    
    # 甘特图代码
    gantt_code = """gantt
    title AI算法进化自组织理论研究时间计划
    dateFormat  YYYY-MM-DD
    section 第一阶段：理论基础构建
    文献调研与理论分析    :a1, 2025-08-01, 2025-09-30
    数学模型构建         :a2, 2025-09-01, 2025-10-31
    理论框架完善         :a3, 2025-10-01, 2025-10-31
    
    section 第二阶段：实验设计与验证
    实验平台扩展         :b1, 2025-11-01, 2025-12-31
    核心实验设计         :b2, 2025-12-01, 2026-02-28
    数据分析与验证       :b3, 2026-02-01, 2026-03-31
    
    section 第三阶段：成果整理与转化
    学术成果产出         :c1, 2026-04-01, 2026-06-30
    技术转化产业化       :c2, 2026-05-01, 2026-07-31
    教育推广           :c3, 2026-06-01, 2026-07-31
    
    section 支撑活动
    文档搜集           :d1, 2025-08-01, 2026-07-31
    过程管理           :d2, 2025-08-01, 2026-07-31
    风险控制           :d3, 2025-08-01, 2026-07-31
    
    section 里程碑
    理论框架完成        :milestone, m1, 2025-10-31, 0d
    实验平台就绪        :milestone, m2, 2025-12-31, 0d
    核心实验完成        :milestone, m3, 2026-02-28, 0d
    论文投稿完成        :milestone, m4, 2026-06-30, 0d
    项目结题           :milestone, m5, 2026-07-31, 0d"""
    
    # 创建文件
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    
    flowchart_filename = f"研究流程图_{timestamp}.mmd"
    gantt_filename = f"研究甘特图_{timestamp}.mmd"
    
    with open(flowchart_filename, 'w', encoding='utf-8') as f:
        f.write(flowchart_code)
    
    with open(gantt_filename, 'w', encoding='utf-8') as f:
        f.write(gantt_code)
    
    return flowchart_filename, gantt_filename, timestamp

def check_mermaid_cli():
    """检查是否安装了mermaid-cli"""
    try:
        result = subprocess.run(['mmdc', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ Mermaid CLI已安装: {result.stdout.strip()}")
            return True
        else:
            return False
    except FileNotFoundError:
        return False

def install_mermaid_cli():
    """安装mermaid-cli"""
    print("📦 正在安装Mermaid CLI...")
    try:
        # 尝试使用npm安装
        result = subprocess.run(['npm', 'install', '-g', '@mermaid-js/mermaid-cli'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Mermaid CLI安装成功!")
            return True
        else:
            print(f"❌ 安装失败: {result.stderr}")
            return False
    except FileNotFoundError:
        print("❌ 未找到npm，请先安装Node.js")
        return False

def generate_images(flowchart_file, gantt_file, timestamp):
    """生成图片文件"""
    
    # 输出文件名
    flowchart_png = f"AI算法进化自组织理论研究流程图_{timestamp}.png"
    flowchart_svg = f"AI算法进化自组织理论研究流程图_{timestamp}.svg"
    gantt_png = f"AI算法进化自组织理论研究甘特图_{timestamp}.png"
    gantt_svg = f"AI算法进化自组织理论研究甘特图_{timestamp}.svg"
    
    generated_files = []
    
    try:
        # 生成流程图PNG
        result = subprocess.run(['mmdc', '-i', flowchart_file, '-o', flowchart_png, 
                               '-w', '1920', '-H', '1080', '--backgroundColor', 'white'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ 流程图PNG生成成功: {flowchart_png}")
            generated_files.append(flowchart_png)
        else:
            print(f"❌ 流程图PNG生成失败: {result.stderr}")
        
        # 生成流程图SVG
        result = subprocess.run(['mmdc', '-i', flowchart_file, '-o', flowchart_svg, 
                               '--backgroundColor', 'white'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ 流程图SVG生成成功: {flowchart_svg}")
            generated_files.append(flowchart_svg)
        else:
            print(f"❌ 流程图SVG生成失败: {result.stderr}")
        
        # 生成甘特图PNG
        result = subprocess.run(['mmdc', '-i', gantt_file, '-o', gantt_png, 
                               '-w', '1920', '-H', '800', '--backgroundColor', 'white'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ 甘特图PNG生成成功: {gantt_png}")
            generated_files.append(gantt_png)
        else:
            print(f"❌ 甘特图PNG生成失败: {result.stderr}")
        
        # 生成甘特图SVG
        result = subprocess.run(['mmdc', '-i', gantt_file, '-o', gantt_svg, 
                               '--backgroundColor', 'white'],
                              capture_output=True, text=True)
        if result.returncode == 0:
            print(f"✅ 甘特图SVG生成成功: {gantt_svg}")
            generated_files.append(gantt_svg)
        else:
            print(f"❌ 甘特图SVG生成失败: {result.stderr}")
            
    except Exception as e:
        print(f"❌ 生成图片时出错: {e}")
    
    return generated_files

def create_html_viewer(flowchart_file, gantt_file, timestamp):
    """创建HTML查看器"""
    
    html_content = f"""<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI算法进化自组织理论研究图表</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        body {{
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }}
        .container {{
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}
        h1, h2 {{
            color: #333;
            text-align: center;
        }}
        .chart-container {{
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background-color: #fafafa;
        }}
        .mermaid {{
            text-align: center;
        }}
        .info {{
            background-color: #e3f2fd;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }}
    </style>
</head>
<body>
    <div class="container">
        <h1>🔬 AI算法进化自组织理论研究图表</h1>
        
        <div class="info">
            <h3>📅 文档信息</h3>
            <p><strong>生成时间:</strong> {datetime.now().strftime('%Y年%m月%d日 %H:%M:%S')}</p>
            <p><strong>图表类型:</strong> 研究流程图 + 甘特图</p>
            <p><strong>用途:</strong> AI算法进化自组织理论研究项目可视化</p>
        </div>
        
        <div class="chart-container">
            <h2>📊 研究流程图</h2>
            <div class="mermaid" id="flowchart">
{open(flowchart_file, 'r', encoding='utf-8').read()}
            </div>
        </div>
        
        <div class="chart-container">
            <h2>📅 研究甘特图</h2>
            <div class="mermaid" id="gantt">
{open(gantt_file, 'r', encoding='utf-8').read()}
            </div>
        </div>
        
        <div class="info">
            <h3>💡 使用说明</h3>
            <ul>
                <li>流程图展示了整个研究项目的完整流程和任务分解</li>
                <li>甘特图显示了12个月的详细时间安排和里程碑</li>
                <li>可以右键保存图片或使用浏览器的打印功能导出PDF</li>
                <li>图表支持缩放和交互操作</li>
            </ul>
        </div>
    </div>
    
    <script>
        mermaid.initialize({{ startOnLoad: true, theme: 'default' }});
    </script>
</body>
</html>"""
    
    html_filename = f"AI算法进化自组织理论研究图表_{timestamp}.html"
    with open(html_filename, 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print(f"✅ HTML查看器生成成功: {html_filename}")
    return html_filename

def main():
    """主函数"""
    print("🚀 AI算法进化自组织理论研究图表生成器启动...")
    print("=" * 60)
    
    # 创建Mermaid文件
    print("📝 创建Mermaid图表文件...")
    flowchart_file, gantt_file, timestamp = create_mermaid_files()
    print(f"✅ Mermaid文件创建成功:")
    print(f"   📊 流程图: {flowchart_file}")
    print(f"   📅 甘特图: {gantt_file}")
    
    # 创建HTML查看器
    print("\\n🌐 创建HTML查看器...")
    html_file = create_html_viewer(flowchart_file, gantt_file, timestamp)
    
    # 检查并安装mermaid-cli
    print("\\n🔍 检查Mermaid CLI...")
    if not check_mermaid_cli():
        print("❌ 未找到Mermaid CLI")
        print("💡 尝试安装Mermaid CLI...")
        if not install_mermaid_cli():
            print("❌ 无法自动安装Mermaid CLI")
            print("💡 请手动安装:")
            print("   1. 安装Node.js: https://nodejs.org/")
            print("   2. 运行命令: npm install -g @mermaid-js/mermaid-cli")
            print("   3. 重新运行此脚本")
            return
    
    # 生成图片
    print("\\n🖼️ 生成图片文件...")
    generated_files = generate_images(flowchart_file, gantt_file, timestamp)
    
    # 总结
    print("\\n🎉 图表生成完成!")
    print("=" * 60)
    print("📋 生成的文件:")
    print(f"   📝 Mermaid源码: {flowchart_file}, {gantt_file}")
    print(f"   🌐 HTML查看器: {html_file}")
    
    if generated_files:
        print("   🖼️ 图片文件:")
        for file in generated_files:
            print(f"      • {file}")
    else:
        print("   ⚠️ 图片文件生成失败，请检查Mermaid CLI安装")
    
    print(f"\\n📁 文件位置: {os.path.abspath('.')}")
    print("💡 建议:")
    print("   • 使用浏览器打开HTML文件查看交互式图表")
    print("   • PNG文件适合插入文档和演示")
    print("   • SVG文件适合网页和矢量编辑")

if __name__ == "__main__":
    main()
