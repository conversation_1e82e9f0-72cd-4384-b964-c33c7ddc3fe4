#!/usr/bin/env python3
"""
Word文档读取器
用于读取Word文档内容并生成Markdown和Word格式
"""

import os
import sys
from datetime import datetime

def read_word_document():
    """读取Word文档内容"""
    try:
        # 尝试使用python-docx库
        from docx import Document
        
        doc_path = "全球人工智能发展：趋势、战略与未来研究方向2025.07.21.docx"
        
        if not os.path.exists(doc_path):
            print(f"❌ 文档不存在: {doc_path}")
            return None
            
        print(f"📖 正在读取文档: {doc_path}")
        
        # 读取Word文档
        doc = Document(doc_path)
        
        # 提取文本内容
        content_lines = []
        
        for paragraph in doc.paragraphs:
            text = paragraph.text.strip()
            if text:  # 只添加非空段落
                content_lines.append(text)
        
        # 提取表格内容
        for table in doc.tables:
            for row in table.rows:
                row_text = []
                for cell in row.cells:
                    cell_text = cell.text.strip()
                    if cell_text:
                        row_text.append(cell_text)
                if row_text:
                    content_lines.append(" | ".join(row_text))
        
        full_content = "\n\n".join(content_lines)
        
        print(f"✅ 文档读取成功!")
        print(f"📊 总段落数: {len(content_lines)}")
        print(f"📝 总字符数: {len(full_content)}")
        
        return full_content
        
    except ImportError:
        print("❌ 缺少python-docx库，正在尝试安装...")
        try:
            import subprocess
            subprocess.check_call([sys.executable, "-m", "pip", "install", "python-docx"])
            print("✅ python-docx安装成功，请重新运行脚本")
            return None
        except Exception as e:
            print(f"❌ 安装python-docx失败: {e}")
            return None
            
    except Exception as e:
        print(f"❌ 读取文档时出错: {e}")
        return None

def create_markdown_document(content):
    """创建Markdown文档"""
    if not content:
        print("❌ 没有内容可以生成Markdown文档")
        return None
        
    timestamp = datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")
    
    markdown_content = f"""# 全球人工智能发展：趋势、战略与未来研究方向

## 📅 文档信息
- **生成时间**: {timestamp}
- **原始文档**: 全球人工智能发展：趋势、战略与未来研究方向2025.07.21.docx
- **研究来源**: 豆包AI助手协作研究
- **文档类型**: 研究报告

---

{content}

---

**📝 文档说明**: 本文档由AI助手从Word格式转换而来，保持了原始内容的完整性。

**🔄 转换时间**: {timestamp}
"""
    
    filename = f"全球人工智能发展研究报告_Markdown_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    
    try:
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(markdown_content)
        print(f"✅ Markdown文档已生成: {filename}")
        return filename
    except Exception as e:
        print(f"❌ 生成Markdown文档时出错: {e}")
        return None

def create_word_document(content):
    """创建新的Word文档"""
    if not content:
        print("❌ 没有内容可以生成Word文档")
        return None
        
    try:
        from docx import Document
        from docx.shared import Inches
        from docx.enum.text import WD_ALIGN_PARAGRAPH
        
        # 创建新文档
        doc = Document()
        
        # 添加标题
        title = doc.add_heading('全球人工智能发展：趋势、战略与未来研究方向', 0)
        title.alignment = WD_ALIGN_PARAGRAPH.CENTER
        
        # 添加文档信息
        doc.add_heading('文档信息', level=1)
        info_para = doc.add_paragraph()
        info_para.add_run('生成时间: ').bold = True
        info_para.add_run(datetime.now().strftime('%Y年%m月%d日 %H:%M:%S'))
        info_para = doc.add_paragraph()
        info_para.add_run('原始文档: ').bold = True
        info_para.add_run('全球人工智能发展：趋势、战略与未来研究方向2025.07.21.docx')
        info_para = doc.add_paragraph()
        info_para.add_run('研究来源: ').bold = True
        info_para.add_run('豆包AI助手协作研究')
        
        # 添加分隔线
        doc.add_paragraph('─' * 50)
        
        # 添加内容
        doc.add_heading('研究报告内容', level=1)
        
        # 将内容按段落分割并添加到文档
        paragraphs = content.split('\n\n')
        for para_text in paragraphs:
            if para_text.strip():
                # 检查是否是标题（简单判断）
                if len(para_text) < 100 and ('第' in para_text or '章' in para_text or '节' in para_text):
                    doc.add_heading(para_text.strip(), level=2)
                else:
                    doc.add_paragraph(para_text.strip())
        
        # 添加结尾信息
        doc.add_paragraph('─' * 50)
        footer_para = doc.add_paragraph()
        footer_para.add_run('文档说明: ').bold = True
        footer_para.add_run('本文档由AI助手从Word格式转换而来，保持了原始内容的完整性。')
        
        footer_para = doc.add_paragraph()
        footer_para.add_run('转换时间: ').bold = True
        footer_para.add_run(datetime.now().strftime('%Y年%m月%d日 %H:%M:%S'))
        
        # 保存文档
        filename = f"全球人工智能发展研究报告_Word_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
        doc.save(filename)
        
        print(f"✅ Word文档已生成: {filename}")
        return filename
        
    except Exception as e:
        print(f"❌ 生成Word文档时出错: {e}")
        return None

def main():
    """主函数"""
    print("🚀 Word文档读取和转换工具启动...")
    print("=" * 60)
    
    # 读取原始Word文档
    content = read_word_document()
    
    if not content:
        print("❌ 无法读取文档内容，程序退出")
        return
    
    print("\n📄 文档内容预览 (前500字符):")
    print("-" * 40)
    print(content[:500] + "..." if len(content) > 500 else content)
    print("-" * 40)
    
    # 生成Markdown文档
    print("\n📝 生成Markdown文档...")
    markdown_file = create_markdown_document(content)
    
    # 生成Word文档
    print("\n📄 生成Word文档...")
    word_file = create_word_document(content)
    
    # 总结
    print("\n🎉 文档转换完成!")
    print("=" * 60)
    print("📋 生成的文件:")
    if markdown_file:
        print(f"   📝 Markdown: {markdown_file}")
    if word_file:
        print(f"   📄 Word: {word_file}")
    
    print(f"\n💡 提示: 生成的文档保持了原始内容的完整性")
    print(f"📁 文件位置: {os.path.abspath('.')}")

if __name__ == "__main__":
    main()
